hoistPattern:
  - '*'
hoistedDependencies:
  '@0no-co/graphql.web@1.1.2(graphql@16.11.0)':
    '@0no-co/graphql.web': private
  '@ag-ui/client@0.0.28':
    '@ag-ui/client': private
  '@ag-ui/core@0.0.28':
    '@ag-ui/core': private
  '@ag-ui/encoder@0.0.28':
    '@ag-ui/encoder': private
  '@ag-ui/proto@0.0.28':
    '@ag-ui/proto': private
  '@anthropic-ai/sdk@0.27.3':
    '@anthropic-ai/sdk': private
  '@babel/runtime@7.27.3':
    '@babel/runtime': private
  '@browserbasehq/sdk@2.6.0':
    '@browserbasehq/sdk': private
  '@browserbasehq/stagehand@1.14.0(@playwright/test@1.52.0)(deepmerge@4.3.1)(dotenv@16.5.0)(openai@4.103.0(ws@8.18.2)(zod@3.25.32))(zod@3.25.32)':
    '@browserbasehq/stagehand': private
  '@bufbuild/protobuf@2.5.1':
    '@bufbuild/protobuf': private
  '@cfworker/json-schema@4.1.1':
    '@cfworker/json-schema': private
  '@copilotkit/runtime-client-gql@1.8.13(graphql@16.11.0)(react@19.1.0)':
    '@copilotkit/runtime-client-gql': private
  '@envelop/core@5.2.3':
    '@envelop/core': private
  '@envelop/instrumentation@1.0.0':
    '@envelop/instrumentation': private
  '@envelop/types@5.2.1':
    '@envelop/types': private
  '@fastify/busboy@3.1.1':
    '@fastify/busboy': private
  '@floating-ui/core@1.7.0':
    '@floating-ui/core': private
  '@floating-ui/dom@1.7.0':
    '@floating-ui/dom': private
  '@floating-ui/react-dom@2.1.2(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@floating-ui/react-dom': private
  '@floating-ui/react@0.26.28(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@floating-ui/react': private
  '@floating-ui/utils@0.2.9':
    '@floating-ui/utils': private
  '@graphql-tools/executor@1.4.7(graphql@16.11.0)':
    '@graphql-tools/executor': private
  '@graphql-tools/merge@9.0.24(graphql@16.11.0)':
    '@graphql-tools/merge': private
  '@graphql-tools/schema@10.0.23(graphql@16.11.0)':
    '@graphql-tools/schema': private
  '@graphql-tools/utils@10.8.6(graphql@16.11.0)':
    '@graphql-tools/utils': private
  '@graphql-typed-document-node/core@3.2.0(graphql@16.11.0)':
    '@graphql-typed-document-node/core': private
  '@graphql-yoga/logger@2.0.1':
    '@graphql-yoga/logger': private
  '@graphql-yoga/plugin-defer-stream@3.13.5(graphql-yoga@5.13.5(graphql@16.11.0))(graphql@16.11.0)':
    '@graphql-yoga/plugin-defer-stream': private
  '@graphql-yoga/subscription@5.0.5':
    '@graphql-yoga/subscription': private
  '@graphql-yoga/typed-event-target@3.0.2':
    '@graphql-yoga/typed-event-target': private
  '@grpc/grpc-js@1.13.4':
    '@grpc/grpc-js': private
  '@grpc/proto-loader@0.7.15':
    '@grpc/proto-loader': private
  '@headlessui/react@2.2.4(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@headlessui/react': private
  '@ibm-cloud/watsonx-ai@1.6.7':
    '@ibm-cloud/watsonx-ai': private
  '@inquirer/checkbox@4.1.8(@types/node@20.17.52)':
    '@inquirer/checkbox': private
  '@inquirer/confirm@5.1.12(@types/node@20.17.52)':
    '@inquirer/confirm': private
  '@inquirer/core@10.1.13(@types/node@20.17.52)':
    '@inquirer/core': private
  '@inquirer/editor@4.2.13(@types/node@20.17.52)':
    '@inquirer/editor': private
  '@inquirer/expand@4.0.15(@types/node@20.17.52)':
    '@inquirer/expand': private
  '@inquirer/figures@1.0.12':
    '@inquirer/figures': private
  '@inquirer/input@4.1.12(@types/node@20.17.52)':
    '@inquirer/input': private
  '@inquirer/number@3.0.15(@types/node@20.17.52)':
    '@inquirer/number': private
  '@inquirer/password@4.0.15(@types/node@20.17.52)':
    '@inquirer/password': private
  '@inquirer/prompts@7.5.3(@types/node@20.17.52)':
    '@inquirer/prompts': private
  '@inquirer/rawlist@4.1.3(@types/node@20.17.52)':
    '@inquirer/rawlist': private
  '@inquirer/search@3.0.15(@types/node@20.17.52)':
    '@inquirer/search': private
  '@inquirer/select@4.2.3(@types/node@20.17.52)':
    '@inquirer/select': private
  '@inquirer/type@3.0.7(@types/node@20.17.52)':
    '@inquirer/type': private
  '@js-sdsl/ordered-map@4.4.2':
    '@js-sdsl/ordered-map': private
  '@langchain/community@0.3.45(@browserbasehq/sdk@2.6.0)(@browserbasehq/stagehand@1.14.0(@playwright/test@1.52.0)(deepmerge@4.3.1)(dotenv@16.5.0)(openai@4.103.0(ws@8.18.2)(zod@3.25.32))(zod@3.25.32))(@ibm-cloud/watsonx-ai@1.6.7)(@langchain/core@0.3.57(openai@4.103.0(ws@8.18.2)(zod@3.25.32)))(@supabase/supabase-js@2.49.8)(axios@1.9.0)(google-auth-library@8.9.0)(ibm-cloud-sdk-core@5.3.2)(ignore@5.3.2)(jsonwebtoken@9.0.2)(openai@4.103.0(ws@8.18.2)(zod@3.25.32))(pg@8.16.0)(playwright@1.52.0)(weaviate-client@3.5.5)(ws@8.18.2)':
    '@langchain/community': private
  '@langchain/google-common@0.1.8(@langchain/core@0.3.57(openai@4.103.0(ws@8.18.2)(zod@3.25.32)))(zod@3.25.32)':
    '@langchain/google-common': private
  '@langchain/google-gauth@0.1.8(@langchain/core@0.3.57(openai@4.103.0(ws@8.18.2)(zod@3.25.32)))(zod@3.25.32)':
    '@langchain/google-gauth': private
  '@langchain/langgraph-checkpoint@0.0.17(@langchain/core@0.3.57(openai@4.103.0(ws@8.18.2)(zod@3.25.32)))':
    '@langchain/langgraph-checkpoint': private
  '@langchain/langgraph-sdk@0.0.70(@langchain/core@0.3.57(openai@4.103.0(ws@8.18.2)(zod@3.25.32)))(react@19.1.0)':
    '@langchain/langgraph-sdk': private
  '@langchain/openai@0.4.9(@langchain/core@0.3.57(openai@4.103.0(ws@8.18.2)(zod@3.25.32)))(ws@8.18.2)':
    '@langchain/openai': private
  '@langchain/textsplitters@0.0.3(openai@4.103.0(ws@8.18.2)(zod@3.25.32))':
    '@langchain/textsplitters': private
  '@langchain/weaviate@0.2.0(@langchain/core@0.3.57(openai@4.103.0(ws@8.18.2)(zod@3.25.32)))':
    '@langchain/weaviate': private
  '@lukeed/csprng@1.1.0':
    '@lukeed/csprng': private
  '@lukeed/uuid@2.0.1':
    '@lukeed/uuid': private
  '@modelcontextprotocol/sdk@1.0.1':
    '@modelcontextprotocol/sdk': private
  '@noble/hashes@1.8.0':
    '@noble/hashes': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@oclif/core@4.3.0':
    '@oclif/core': private
  '@paralleldrive/cuid2@2.2.2':
    '@paralleldrive/cuid2': private
  '@playwright/test@1.52.0':
    '@playwright/test': private
  '@protobufjs/aspromise@1.1.2':
    '@protobufjs/aspromise': private
  '@protobufjs/base64@1.1.2':
    '@protobufjs/base64': private
  '@protobufjs/codegen@2.0.4':
    '@protobufjs/codegen': private
  '@protobufjs/eventemitter@1.1.0':
    '@protobufjs/eventemitter': private
  '@protobufjs/fetch@1.1.0':
    '@protobufjs/fetch': private
  '@protobufjs/float@1.0.2':
    '@protobufjs/float': private
  '@protobufjs/inquire@1.1.0':
    '@protobufjs/inquire': private
  '@protobufjs/path@1.1.2':
    '@protobufjs/path': private
  '@protobufjs/pool@1.1.0':
    '@protobufjs/pool': private
  '@protobufjs/utf8@1.1.0':
    '@protobufjs/utf8': private
  '@react-aria/focus@3.20.3(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@react-aria/focus': private
  '@react-aria/interactions@3.25.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@react-aria/interactions': private
  '@react-aria/ssr@3.9.8(react@19.1.0)':
    '@react-aria/ssr': private
  '@react-aria/utils@3.29.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@react-aria/utils': private
  '@react-stately/flags@3.1.1':
    '@react-stately/flags': private
  '@react-stately/utils@3.10.6(react@19.1.0)':
    '@react-stately/utils': private
  '@react-types/shared@3.29.1(react@19.1.0)':
    '@react-types/shared': private
  '@repeaterjs/repeater@3.0.6':
    '@repeaterjs/repeater': private
  '@scarf/scarf@1.4.0':
    '@scarf/scarf': private
  '@segment/analytics-core@1.8.1':
    '@segment/analytics-core': private
  '@segment/analytics-generic-utils@1.2.0':
    '@segment/analytics-generic-utils': private
  '@segment/analytics-node@2.2.1':
    '@segment/analytics-node': private
  '@sentry-internal/tracing@7.120.3':
    '@sentry-internal/tracing': private
  '@sentry/core@7.120.3':
    '@sentry/core': private
  '@sentry/integrations@7.120.3':
    '@sentry/integrations': private
  '@sentry/node@7.120.3':
    '@sentry/node': private
  '@sentry/types@7.120.3':
    '@sentry/types': private
  '@sentry/utils@7.120.3':
    '@sentry/utils': private
  '@supabase/auth-js@2.69.1':
    '@supabase/auth-js': private
  '@supabase/functions-js@2.4.4':
    '@supabase/functions-js': private
  '@supabase/node-fetch@2.6.15':
    '@supabase/node-fetch': private
  '@supabase/postgrest-js@1.19.4':
    '@supabase/postgrest-js': private
  '@supabase/realtime-js@2.11.2':
    '@supabase/realtime-js': private
  '@supabase/storage-js@2.7.1':
    '@supabase/storage-js': private
  '@swc/helpers@0.5.17':
    '@swc/helpers': private
  '@tanstack/react-virtual@3.13.9(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@tanstack/react-virtual': private
  '@tanstack/virtual-core@3.13.9':
    '@tanstack/virtual-core': private
  '@tokenizer/token@0.3.0':
    '@tokenizer/token': private
  '@trpc/client@11.1.3(@trpc/server@11.1.3(typescript@5.8.3))(typescript@5.8.3)':
    '@trpc/client': private
  '@trpc/server@11.1.3(typescript@5.8.3)':
    '@trpc/server': private
  '@types/cookie@0.6.0':
    '@types/cookie': private
  '@types/debug@4.1.12':
    '@types/debug': private
  '@types/estree-jsx@1.0.5':
    '@types/estree-jsx': private
  '@types/estree@1.0.7':
    '@types/estree': private
  '@types/hast@3.0.4':
    '@types/hast': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/katex@0.16.7':
    '@types/katex': private
  '@types/mdast@4.0.4':
    '@types/mdast': private
  '@types/ms@2.1.0':
    '@types/ms': private
  '@types/node-fetch@2.6.12':
    '@types/node-fetch': private
  '@types/phoenix@1.6.6':
    '@types/phoenix': private
  '@types/prop-types@15.7.14':
    '@types/prop-types': private
  '@types/react@19.1.6':
    '@types/react': private
  '@types/retry@0.12.0':
    '@types/retry': private
  '@types/semver@7.7.0':
    '@types/semver': private
  '@types/tough-cookie@4.0.5':
    '@types/tough-cookie': private
  '@types/unist@2.0.11':
    '@types/unist': private
  '@types/uuid@10.0.0':
    '@types/uuid': private
  '@types/validator@13.15.1':
    '@types/validator': private
  '@types/ws@8.18.1':
    '@types/ws': private
  '@ungap/structured-clone@1.3.0':
    '@ungap/structured-clone': private
  '@urql/core@5.1.1(graphql@16.11.0)':
    '@urql/core': private
  '@whatwg-node/disposablestack@0.0.6':
    '@whatwg-node/disposablestack': private
  '@whatwg-node/events@0.1.2':
    '@whatwg-node/events': private
  '@whatwg-node/fetch@0.10.8':
    '@whatwg-node/fetch': private
  '@whatwg-node/node-fetch@0.7.21':
    '@whatwg-node/node-fetch': private
  '@whatwg-node/promise-helpers@1.3.2':
    '@whatwg-node/promise-helpers': private
  '@whatwg-node/server@0.10.10':
    '@whatwg-node/server': private
  abort-controller-x@0.4.3:
    abort-controller-x: private
  abort-controller@3.0.0:
    abort-controller: private
  accepts@1.3.8:
    accepts: private
  agent-base@6.0.2:
    agent-base: private
  agentkeepalive@4.6.0:
    agentkeepalive: private
  ajv-formats@3.0.1(ajv@8.17.1):
    ajv-formats: private
  ajv@8.17.1:
    ajv: private
  ansi-escapes@4.3.2:
    ansi-escapes: private
  ansi-regex@6.1.0:
    ansi-regex: private
  ansi-styles@5.2.0:
    ansi-styles: private
  ansis@3.17.0:
    ansis: private
  argparse@2.0.1:
    argparse: private
  array-flatten@1.1.1:
    array-flatten: private
  array-union@2.1.0:
    array-union: private
  arrify@2.0.1:
    arrify: private
  async@3.2.6:
    async: private
  asynckit@0.4.0:
    asynckit: private
  atomic-sleep@1.0.0:
    atomic-sleep: private
  atomically@2.0.3:
    atomically: private
  axios@1.9.0:
    axios: private
  bail@2.0.2:
    bail: private
  balanced-match@1.0.2:
    balanced-match: private
  base-64@0.1.0:
    base-64: private
  base64-js@1.5.1:
    base64-js: private
  bignumber.js@9.3.0:
    bignumber.js: private
  binary-extensions@2.3.0:
    binary-extensions: private
  binary-search@1.3.6:
    binary-search: private
  body-parser@1.20.3:
    body-parser: private
  brace-expansion@2.0.1:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  buffer-equal-constant-time@1.0.1:
    buffer-equal-constant-time: private
  buffer@6.0.3:
    buffer: private
  bundle-name@4.1.0:
    bundle-name: private
  bytes@3.1.2:
    bytes: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bound@1.0.4:
    call-bound: private
  camelcase@6.3.0:
    camelcase: private
  ccount@2.0.1:
    ccount: private
  chalk@5.4.1:
    chalk: private
  character-entities-html4@2.1.0:
    character-entities-html4: private
  character-entities-legacy@1.1.4:
    character-entities-legacy: private
  character-entities@1.2.4:
    character-entities: private
  character-reference-invalid@1.1.4:
    character-reference-invalid: private
  chardet@0.7.0:
    chardet: private
  charenc@0.0.2:
    charenc: private
  class-transformer@0.5.1:
    class-transformer: private
  class-validator@0.14.2:
    class-validator: private
  clean-stack@3.0.1:
    clean-stack: private
  cli-cursor@5.0.0:
    cli-cursor: private
  cli-spinners@2.9.2:
    cli-spinners: private
  cli-width@4.1.0:
    cli-width: private
  cliui@7.0.4:
    cliui: private
  clone@2.1.2:
    clone: private
  clsx@2.1.1:
    clsx: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  colorette@2.0.20:
    colorette: private
  combined-stream@1.0.8:
    combined-stream: private
  comma-separated-tokens@2.0.3:
    comma-separated-tokens: private
  commander@10.0.1:
    commander: private
  concat-map@0.0.1:
    concat-map: private
  conf@13.1.0:
    conf: private
  console-table-printer@2.14.0:
    console-table-printer: private
  content-disposition@0.5.4:
    content-disposition: private
  content-type@1.0.5:
    content-type: private
  cookie-signature@1.0.6:
    cookie-signature: private
  cookie@0.7.2:
    cookie: private
  copy-anything@3.0.5:
    copy-anything: private
  cors@2.8.5:
    cors: private
  cross-fetch@3.2.0:
    cross-fetch: private
  cross-inspect@1.0.1:
    cross-inspect: private
  crypt@0.0.2:
    crypt: private
  csstype@3.1.3:
    csstype: private
  dateformat@4.6.3:
    dateformat: private
  debounce-fn@6.0.0:
    debounce-fn: private
  debug@4.4.1(supports-color@8.1.1):
    debug: private
  decamelize@1.2.0:
    decamelize: private
  decode-named-character-reference@1.1.0:
    decode-named-character-reference: private
  deepmerge@4.3.1:
    deepmerge: private
  default-browser-id@5.0.0:
    default-browser-id: private
  default-browser@5.2.1:
    default-browser: private
  define-lazy-prop@3.0.0:
    define-lazy-prop: private
  delayed-stream@1.0.0:
    delayed-stream: private
  depd@2.0.0:
    depd: private
  dequal@2.0.3:
    dequal: private
  destroy@1.2.0:
    destroy: private
  devlop@1.1.0:
    devlop: private
  diff@5.2.0:
    diff: private
  digest-fetch@1.3.0:
    digest-fetch: private
  dir-glob@3.0.1:
    dir-glob: private
  dot-prop@9.0.0:
    dot-prop: private
  dset@3.1.4:
    dset: private
  dunder-proto@1.0.1:
    dunder-proto: private
  ecdsa-sig-formatter@1.0.11:
    ecdsa-sig-formatter: private
  ee-first@1.1.1:
    ee-first: private
  ejs@3.1.10:
    ejs: private
  emoji-regex@8.0.0:
    emoji-regex: private
  encodeurl@2.0.0:
    encodeurl: private
  end-of-stream@1.4.4:
    end-of-stream: private
  entities@6.0.0:
    entities: private
  env-paths@3.0.0:
    env-paths: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  escalade@3.2.0:
    escalade: private
  escape-html@1.0.3:
    escape-html: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  estree-util-is-identifier-name@3.0.0:
    estree-util-is-identifier-name: private
  etag@1.8.1:
    etag: private
  event-target-shim@5.0.1:
    event-target-shim: private
  eventemitter3@4.0.7:
    eventemitter3: private
  events@3.3.0:
    events: private
  expr-eval@2.0.2:
    expr-eval: private
  express@4.21.2:
    express: private
  extend@3.0.2:
    extend: private
  external-editor@3.1.0:
    external-editor: private
  fast-copy@3.0.2:
    fast-copy: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-patch@3.1.1:
    fast-json-patch: private
  fast-redact@3.5.0:
    fast-redact: private
  fast-safe-stringify@2.1.1:
    fast-safe-stringify: private
  fast-text-encoding@1.0.6:
    fast-text-encoding: private
  fast-uri@3.0.6:
    fast-uri: private
  fastq@1.19.1:
    fastq: private
  fault@1.0.4:
    fault: private
  file-type@16.5.4:
    file-type: private
  filelist@1.0.4:
    filelist: private
  fill-range@7.1.1:
    fill-range: private
  finalhandler@1.3.1:
    finalhandler: private
  flat@5.0.2:
    flat: private
  follow-redirects@1.15.9(debug@4.3.2):
    follow-redirects: private
  form-data-encoder@1.7.2:
    form-data-encoder: private
  form-data@4.0.2:
    form-data: private
  format@0.2.2:
    format: private
  formdata-node@4.4.1:
    formdata-node: private
  forwarded@0.2.0:
    forwarded: private
  fresh@0.5.2:
    fresh: private
  fsevents@2.3.2:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  gaxios@5.1.3:
    gaxios: private
  gcp-metadata@5.3.0:
    gcp-metadata: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-east-asian-width@1.3.0:
    get-east-asian-width: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-package-type@0.1.0:
    get-package-type: private
  get-port@7.1.0:
    get-port: private
  get-proto@1.0.1:
    get-proto: private
  glob-parent@5.1.2:
    glob-parent: private
  globby@11.1.0:
    globby: private
  google-auth-library@8.9.0:
    google-auth-library: private
  google-p12-pem@4.0.1:
    google-p12-pem: private
  gopd@1.2.0:
    gopd: private
  graphql-query-complexity@0.12.0(graphql@16.11.0):
    graphql-query-complexity: private
  graphql-request@6.1.0(graphql@16.11.0):
    graphql-request: private
  graphql-scalars@1.24.2(graphql@16.11.0):
    graphql-scalars: private
  graphql-yoga@5.13.5(graphql@16.11.0):
    graphql-yoga: private
  graphql@16.11.0:
    graphql: private
  groq-sdk@0.5.0:
    groq-sdk: private
  gtoken@6.1.2:
    gtoken: private
  has-flag@4.0.0:
    has-flag: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  hast-util-from-parse5@8.0.3:
    hast-util-from-parse5: private
  hast-util-parse-selector@2.2.5:
    hast-util-parse-selector: private
  hast-util-raw@9.1.0:
    hast-util-raw: private
  hast-util-to-jsx-runtime@2.3.6:
    hast-util-to-jsx-runtime: private
  hast-util-to-parse5@8.0.0:
    hast-util-to-parse5: private
  hast-util-whitespace@2.0.1:
    hast-util-whitespace: private
  hastscript@6.0.0:
    hastscript: private
  help-me@5.0.0:
    help-me: private
  highlight.js@10.7.3:
    highlight.js: private
  highlightjs-vue@1.0.0:
    highlightjs-vue: private
  html-url-attributes@3.0.1:
    html-url-attributes: private
  html-void-elements@3.0.0:
    html-void-elements: private
  http-errors@2.0.0:
    http-errors: private
  https-proxy-agent@5.0.1:
    https-proxy-agent: private
  humanize-ms@1.2.1:
    humanize-ms: private
  ibm-cloud-sdk-core@5.3.2:
    ibm-cloud-sdk-core: private
  iconv-lite@0.4.24:
    iconv-lite: private
  ieee754@1.2.1:
    ieee754: private
  ignore@5.3.2:
    ignore: private
  immediate@3.0.6:
    immediate: private
  indent-string@4.0.0:
    indent-string: private
  inherits@2.0.4:
    inherits: private
  inline-style-parser@0.1.1:
    inline-style-parser: private
  inquirer@12.6.3(@types/node@20.17.52):
    inquirer: private
  ipaddr.js@1.9.1:
    ipaddr.js: private
  is-alphabetical@1.0.4:
    is-alphabetical: private
  is-alphanumerical@1.0.4:
    is-alphanumerical: private
  is-any-array@2.0.1:
    is-any-array: private
  is-buffer@2.0.5:
    is-buffer: private
  is-decimal@1.0.4:
    is-decimal: private
  is-docker@3.0.0:
    is-docker: private
  is-extglob@2.1.1:
    is-extglob: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-glob@4.0.3:
    is-glob: private
  is-hexadecimal@1.0.4:
    is-hexadecimal: private
  is-inside-container@1.0.0:
    is-inside-container: private
  is-interactive@2.0.0:
    is-interactive: private
  is-number@7.0.0:
    is-number: private
  is-plain-obj@4.1.0:
    is-plain-obj: private
  is-stream@2.0.1:
    is-stream: private
  is-unicode-supported@2.1.0:
    is-unicode-supported: private
  is-what@4.1.16:
    is-what: private
  is-wsl@2.2.0:
    is-wsl: private
  isstream@0.1.2:
    isstream: private
  jake@10.9.2:
    jake: private
  jose@5.10.0:
    jose: private
  joycon@3.1.1:
    joycon: private
  js-tiktoken@1.0.20:
    js-tiktoken: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  json-bigint@1.0.0:
    json-bigint: private
  json-schema-traverse@1.0.0:
    json-schema-traverse: private
  json-schema-typed@8.0.1:
    json-schema-typed: private
  jsonpointer@5.0.1:
    jsonpointer: private
  jsonwebtoken@9.0.2:
    jsonwebtoken: private
  jwa@2.0.1:
    jwa: private
  jws@4.0.0:
    jws: private
  katex@0.16.22:
    katex: private
  kleur@4.1.5:
    kleur: private
  langchainhub@0.0.11:
    langchainhub: private
  langsmith@0.3.29(openai@4.103.0(ws@8.18.2)(zod@3.25.32)):
    langsmith: private
  libphonenumber-js@1.12.8:
    libphonenumber-js: private
  lie@3.1.1:
    lie: private
  lilconfig@3.1.3:
    lilconfig: private
  localforage@1.10.0:
    localforage: private
  localtunnel@2.0.2:
    localtunnel: private
  lodash.camelcase@4.3.0:
    lodash.camelcase: private
  lodash.get@4.4.2:
    lodash.get: private
  lodash.includes@4.3.0:
    lodash.includes: private
  lodash.isboolean@3.0.3:
    lodash.isboolean: private
  lodash.isinteger@4.0.4:
    lodash.isinteger: private
  lodash.isnumber@3.0.3:
    lodash.isnumber: private
  lodash.isplainobject@4.0.6:
    lodash.isplainobject: private
  lodash.isstring@4.0.1:
    lodash.isstring: private
  lodash.once@4.1.1:
    lodash.once: private
  log-symbols@6.0.0:
    log-symbols: private
  long@5.3.2:
    long: private
  longest-streak@3.1.0:
    longest-streak: private
  loose-envify@1.4.0:
    loose-envify: private
  lowlight@1.20.0:
    lowlight: private
  lru-cache@6.0.0:
    lru-cache: private
  markdown-table@3.0.4:
    markdown-table: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  md5@2.3.0:
    md5: private
  mdast-util-definitions@5.1.2:
    mdast-util-definitions: private
  mdast-util-find-and-replace@3.0.2:
    mdast-util-find-and-replace: private
  mdast-util-from-markdown@2.0.2:
    mdast-util-from-markdown: private
  mdast-util-gfm-autolink-literal@2.0.1:
    mdast-util-gfm-autolink-literal: private
  mdast-util-gfm-footnote@2.1.0:
    mdast-util-gfm-footnote: private
  mdast-util-gfm-strikethrough@2.0.0:
    mdast-util-gfm-strikethrough: private
  mdast-util-gfm-table@2.0.0:
    mdast-util-gfm-table: private
  mdast-util-gfm-task-list-item@2.0.0:
    mdast-util-gfm-task-list-item: private
  mdast-util-gfm@3.1.0:
    mdast-util-gfm: private
  mdast-util-math@3.0.0:
    mdast-util-math: private
  mdast-util-mdx-expression@2.0.1:
    mdast-util-mdx-expression: private
  mdast-util-mdx-jsx@3.2.0:
    mdast-util-mdx-jsx: private
  mdast-util-mdxjs-esm@2.0.1:
    mdast-util-mdxjs-esm: private
  mdast-util-phrasing@4.1.0:
    mdast-util-phrasing: private
  mdast-util-to-hast@13.2.0:
    mdast-util-to-hast: private
  mdast-util-to-markdown@2.1.2:
    mdast-util-to-markdown: private
  mdast-util-to-string@3.2.0:
    mdast-util-to-string: private
  media-typer@0.3.0:
    media-typer: private
  merge-descriptors@1.0.3:
    merge-descriptors: private
  merge2@1.4.1:
    merge2: private
  methods@1.1.2:
    methods: private
  micromark-core-commonmark@2.0.3:
    micromark-core-commonmark: private
  micromark-extension-gfm-autolink-literal@2.1.0:
    micromark-extension-gfm-autolink-literal: private
  micromark-extension-gfm-footnote@2.1.0:
    micromark-extension-gfm-footnote: private
  micromark-extension-gfm-strikethrough@2.1.0:
    micromark-extension-gfm-strikethrough: private
  micromark-extension-gfm-table@2.1.1:
    micromark-extension-gfm-table: private
  micromark-extension-gfm-tagfilter@2.0.0:
    micromark-extension-gfm-tagfilter: private
  micromark-extension-gfm-task-list-item@2.1.0:
    micromark-extension-gfm-task-list-item: private
  micromark-extension-gfm@3.0.0:
    micromark-extension-gfm: private
  micromark-extension-math@3.1.0:
    micromark-extension-math: private
  micromark-factory-destination@1.1.0:
    micromark-factory-destination: private
  micromark-factory-label@1.1.0:
    micromark-factory-label: private
  micromark-factory-space@2.0.1:
    micromark-factory-space: private
  micromark-factory-title@1.1.0:
    micromark-factory-title: private
  micromark-factory-whitespace@1.1.0:
    micromark-factory-whitespace: private
  micromark-util-character@2.1.1:
    micromark-util-character: private
  micromark-util-chunked@2.0.1:
    micromark-util-chunked: private
  micromark-util-classify-character@2.0.1:
    micromark-util-classify-character: private
  micromark-util-combine-extensions@2.0.1:
    micromark-util-combine-extensions: private
  micromark-util-decode-numeric-character-reference@1.1.0:
    micromark-util-decode-numeric-character-reference: private
  micromark-util-decode-string@1.1.0:
    micromark-util-decode-string: private
  micromark-util-encode@2.0.1:
    micromark-util-encode: private
  micromark-util-html-tag-name@1.2.0:
    micromark-util-html-tag-name: private
  micromark-util-normalize-identifier@1.1.0:
    micromark-util-normalize-identifier: private
  micromark-util-resolve-all@2.0.1:
    micromark-util-resolve-all: private
  micromark-util-sanitize-uri@2.0.1:
    micromark-util-sanitize-uri: private
  micromark-util-subtokenize@1.1.0:
    micromark-util-subtokenize: private
  micromark-util-symbol@2.0.1:
    micromark-util-symbol: private
  micromark-util-types@2.0.2:
    micromark-util-types: private
  micromark@3.2.0:
    micromark: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mime@1.6.0:
    mime: private
  mimic-function@5.0.1:
    mimic-function: private
  minimatch@9.0.5:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  ml-array-mean@1.1.6:
    ml-array-mean: private
  ml-array-sum@1.1.6:
    ml-array-sum: private
  ml-distance-euclidean@2.0.0:
    ml-distance-euclidean: private
  ml-distance@4.0.1:
    ml-distance: private
  ml-tree-similarity@1.0.0:
    ml-tree-similarity: private
  mri@1.2.0:
    mri: private
  ms@2.0.0:
    ms: private
  mustache@4.2.0:
    mustache: private
  mute-stream@2.0.0:
    mute-stream: private
  negotiator@0.6.3:
    negotiator: private
  nice-grpc-client-middleware-retry@3.1.11:
    nice-grpc-client-middleware-retry: private
  nice-grpc-common@2.0.2:
    nice-grpc-common: private
  nice-grpc@2.1.12:
    nice-grpc: private
  node-domexception@1.0.0:
    node-domexception: private
  node-forge@1.3.1:
    node-forge: private
  num-sort@2.1.0:
    num-sort: private
  object-assign@4.1.1:
    object-assign: private
  object-inspect@1.13.4:
    object-inspect: private
  on-exit-leak-free@2.1.2:
    on-exit-leak-free: private
  on-finished@2.4.1:
    on-finished: private
  once@1.4.0:
    once: private
  onetime@7.0.0:
    onetime: private
  open@10.1.2:
    open: private
  openapi-types@12.1.3:
    openapi-types: private
  openurl@1.1.1:
    openurl: private
  ora@8.2.0:
    ora: private
  os-tmpdir@1.0.2:
    os-tmpdir: private
  p-finally@1.0.0:
    p-finally: private
  p-queue@6.6.2:
    p-queue: private
  p-retry@4.6.2:
    p-retry: private
  p-timeout@3.2.0:
    p-timeout: private
  parse-entities@2.0.0:
    parse-entities: private
  parse5@7.3.0:
    parse5: private
  parseurl@1.3.3:
    parseurl: private
  partial-json@0.1.7:
    partial-json: private
  path-to-regexp@0.1.12:
    path-to-regexp: private
  path-type@4.0.0:
    path-type: private
  peek-readable@4.1.0:
    peek-readable: private
  pg-cloudflare@1.2.5:
    pg-cloudflare: private
  pg-connection-string@2.9.0:
    pg-connection-string: private
  pg-int8@1.0.1:
    pg-int8: private
  pg-pool@3.10.0(pg@8.16.0):
    pg-pool: private
  pg-protocol@1.10.0:
    pg-protocol: private
  pg-types@2.2.0:
    pg-types: private
  pgpass@1.0.5:
    pgpass: private
  picomatch@2.3.1:
    picomatch: private
  pino-abstract-transport@2.0.0:
    pino-abstract-transport: private
  pino-pretty@11.3.0:
    pino-pretty: private
  pino-std-serializers@7.0.0:
    pino-std-serializers: private
  pino@9.7.0:
    pino: private
  playwright-core@1.52.0:
    playwright-core: private
  playwright@1.52.0:
    playwright: private
  postgres-array@2.0.0:
    postgres-array: private
  postgres-bytea@1.0.0:
    postgres-bytea: private
  postgres-date@1.0.7:
    postgres-date: private
  postgres-interval@1.2.0:
    postgres-interval: private
  prismjs@1.30.0:
    prismjs: private
  process-warning@5.0.0:
    process-warning: private
  process@0.11.10:
    process: private
  prop-types@15.8.1:
    prop-types: private
  property-information@6.5.0:
    property-information: private
  protobufjs@7.4.0:
    protobufjs: private
  proxy-addr@2.0.7:
    proxy-addr: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  psl@1.15.0:
    psl: private
  pump@3.0.2:
    pump: private
  punycode@2.3.1:
    punycode: private
  qs@6.13.0:
    qs: private
  querystringify@2.2.0:
    querystringify: private
  queue-microtask@1.2.3:
    queue-microtask: private
  quick-format-unescaped@4.0.4:
    quick-format-unescaped: private
  range-parser@1.2.1:
    range-parser: private
  raw-body@3.0.0:
    raw-body: private
  react-dom@19.1.0(react@19.1.0):
    react-dom: private
  react-is@18.3.1:
    react-is: private
  react-markdown@8.0.7(@types/react@19.1.6)(react@19.1.0):
    react-markdown: private
  react-syntax-highlighter@15.6.1(react@19.1.0):
    react-syntax-highlighter: private
  react@19.1.0:
    react: private
  readable-stream@4.7.0:
    readable-stream: private
  readable-web-to-node-stream@3.0.4:
    readable-web-to-node-stream: private
  real-require@0.2.0:
    real-require: private
  reflect-metadata@0.2.2:
    reflect-metadata: private
  refractor@3.6.0:
    refractor: private
  rehype-raw@7.0.0:
    rehype-raw: private
  remark-gfm@4.0.1:
    remark-gfm: private
  remark-math@6.0.0:
    remark-math: private
  remark-parse@11.0.0:
    remark-parse: private
  remark-rehype@11.1.2:
    remark-rehype: private
  remark-stringify@11.0.0:
    remark-stringify: private
  require-directory@2.1.1:
    require-directory: private
  require-from-string@2.0.2:
    require-from-string: private
  requires-port@1.0.0:
    requires-port: private
  restore-cursor@5.1.0:
    restore-cursor: private
  retry-axios@2.6.0(axios@1.9.0):
    retry-axios: private
  retry@0.13.1:
    retry: private
  reusify@1.1.0:
    reusify: private
  run-applescript@7.0.0:
    run-applescript: private
  run-async@3.0.0:
    run-async: private
  run-parallel@1.2.0:
    run-parallel: private
  rxjs@7.8.2:
    rxjs: private
  sade@1.8.1:
    sade: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safe-stable-stringify@2.5.0:
    safe-stable-stringify: private
  safer-buffer@2.1.2:
    safer-buffer: private
  scheduler@0.26.0:
    scheduler: private
  secure-json-parse@2.7.0:
    secure-json-parse: private
  semver@7.7.2:
    semver: private
  send@0.19.0:
    send: private
  serve-static@1.16.2:
    serve-static: private
  setprototypeof@1.2.0:
    setprototypeof: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  signal-exit@4.1.0:
    signal-exit: private
  simple-wcswidth@1.0.1:
    simple-wcswidth: private
  slash@3.0.0:
    slash: private
  sonic-boom@4.2.0:
    sonic-boom: private
  space-separated-tokens@2.0.2:
    space-separated-tokens: private
  split2@4.2.0:
    split2: private
  statuses@2.0.1:
    statuses: private
  stdin-discarder@0.2.2:
    stdin-discarder: private
  string-width@4.2.3:
    string-width: private
  string_decoder@1.3.0:
    string_decoder: private
  stringify-entities@4.0.4:
    stringify-entities: private
  strip-ansi@7.1.0:
    strip-ansi: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  strtok3@6.3.0:
    strtok3: private
  stubborn-fs@1.2.5:
    stubborn-fs: private
  style-to-js@1.1.16:
    style-to-js: private
  style-to-object@0.4.4:
    style-to-object: private
  superjson@2.2.2:
    superjson: private
  supports-color@8.1.1:
    supports-color: private
  tabbable@6.2.0:
    tabbable: private
  thread-stream@3.1.0:
    thread-stream: private
  tmp@0.0.33:
    tmp: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toidentifier@1.0.1:
    toidentifier: private
  token-types@4.2.1:
    token-types: private
  tough-cookie@4.1.4:
    tough-cookie: private
  tr46@0.0.3:
    tr46: private
  trim-lines@3.0.1:
    trim-lines: private
  trough@2.2.0:
    trough: private
  ts-error@1.0.6:
    ts-error: private
  tslib@2.8.1:
    tslib: private
  type-fest@0.21.3:
    type-fest: private
  type-graphql@2.0.0-rc.1(class-validator@0.14.2)(graphql-scalars@1.24.2(graphql@16.11.0))(graphql@16.11.0):
    type-graphql: private
  type-is@1.6.18:
    type-is: private
  uint8array-extras@1.4.0:
    uint8array-extras: private
  undici-types@6.19.8:
    undici-types: private
  unified@11.0.5:
    unified: private
  unist-util-generated@2.0.1:
    unist-util-generated: private
  unist-util-is@5.2.1:
    unist-util-is: private
  unist-util-position@5.0.0:
    unist-util-position: private
  unist-util-remove-position@5.0.0:
    unist-util-remove-position: private
  unist-util-stringify-position@3.0.3:
    unist-util-stringify-position: private
  unist-util-visit-parents@5.1.3:
    unist-util-visit-parents: private
  unist-util-visit@5.0.0:
    unist-util-visit: private
  universalify@0.2.0:
    universalify: private
  unpipe@1.0.0:
    unpipe: private
  untruncate-json@0.0.1:
    untruncate-json: private
  url-parse@1.5.10:
    url-parse: private
  urlpattern-polyfill@10.1.0:
    urlpattern-polyfill: private
  urql@4.2.2(@urql/core@5.1.1(graphql@16.11.0))(react@19.1.0):
    urql: private
  use-sync-external-store@1.5.0(react@19.1.0):
    use-sync-external-store: private
  utils-merge@1.0.1:
    utils-merge: private
  uuid@10.0.0:
    uuid: private
  uvu@0.5.6:
    uvu: private
  validator@13.15.15:
    validator: private
  vary@1.1.2:
    vary: private
  vfile-location@5.0.3:
    vfile-location: private
  vfile-message@4.0.2:
    vfile-message: private
  vfile@6.0.3:
    vfile: private
  weaviate-client@3.5.5:
    weaviate-client: private
  web-namespaces@2.0.1:
    web-namespaces: private
  web-streams-polyfill@3.3.3:
    web-streams-polyfill: private
  webidl-conversions@3.0.1:
    webidl-conversions: private
  whatwg-url@5.0.0:
    whatwg-url: private
  when-exit@2.1.4:
    when-exit: private
  widest-line@3.1.0:
    widest-line: private
  wonka@6.3.5:
    wonka: private
  wordwrap@1.0.0:
    wordwrap: private
  wrap-ansi@7.0.0:
    wrap-ansi: private
  wrappy@1.0.2:
    wrappy: private
  ws@8.18.2:
    ws: private
  xtend@4.0.2:
    xtend: private
  y18n@5.0.8:
    y18n: private
  yallist@4.0.0:
    yallist: private
  yaml@2.8.0:
    yaml: private
  yargs-parser@20.2.9:
    yargs-parser: private
  yargs@17.1.1:
    yargs: private
  yoctocolors-cjs@2.1.2:
    yoctocolors-cjs: private
  zod-to-json-schema@3.24.5(zod@3.25.32):
    zod-to-json-schema: private
  zod@3.25.32:
    zod: private
  zwitch@2.0.4:
    zwitch: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@9.13.2
pendingBuilds: []
prunedAt: Wed, 28 May 2025 19:58:25 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: https://registry.npmjs.org/
skipped: []
storeDir: /Users/<USER>/Library/pnpm/store/v3
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
