#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/@modelcontextprotocol+server-postgres@0.6.2/node_modules/@modelcontextprotocol/server-postgres/dist/node_modules:/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/@modelcontextprotocol+server-postgres@0.6.2/node_modules/@modelcontextprotocol/server-postgres/node_modules:/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/@modelcontextprotocol+server-postgres@0.6.2/node_modules/@modelcontextprotocol/node_modules:/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/@modelcontextprotocol+server-postgres@0.6.2/node_modules:/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/@modelcontextprotocol+server-postgres@0.6.2/node_modules/@modelcontextprotocol/server-postgres/dist/node_modules:/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/@modelcontextprotocol+server-postgres@0.6.2/node_modules/@modelcontextprotocol/server-postgres/node_modules:/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/@modelcontextprotocol+server-postgres@0.6.2/node_modules/@modelcontextprotocol/node_modules:/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/@modelcontextprotocol+server-postgres@0.6.2/node_modules:/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../dist/index.js" "$@"
else
  exec node  "$basedir/../../dist/index.js" "$@"
fi
