#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/jake@10.9.2/node_modules/jake/bin/node_modules:/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/jake@10.9.2/node_modules/jake/node_modules:/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/jake@10.9.2/node_modules:/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/jake@10.9.2/node_modules/jake/bin/node_modules:/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/jake@10.9.2/node_modules/jake/node_modules:/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/jake@10.9.2/node_modules:/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../jake@10.9.2/node_modules/jake/bin/cli.js" "$@"
else
  exec node  "$basedir/../../../../../jake@10.9.2/node_modules/jake/bin/cli.js" "$@"
fi
