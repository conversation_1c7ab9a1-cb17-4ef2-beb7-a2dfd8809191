#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/uvu@0.5.6/node_modules/uvu/node_modules:/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/uvu@0.5.6/node_modules:/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/uvu@0.5.6/node_modules/uvu/node_modules:/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/uvu@0.5.6/node_modules:/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../uvu@0.5.6/node_modules/uvu/bin.js" "$@"
else
  exec node  "$basedir/../../../../../uvu@0.5.6/node_modules/uvu/bin.js" "$@"
fi
