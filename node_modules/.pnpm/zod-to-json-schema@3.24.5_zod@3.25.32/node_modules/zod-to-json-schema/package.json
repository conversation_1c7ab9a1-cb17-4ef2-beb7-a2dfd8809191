{"name": "zod-to-json-schema", "version": "3.24.5", "description": "Converts <PERSON><PERSON> schemas to <PERSON><PERSON>", "types": "./dist/types/index.d.ts", "main": "./dist/cjs/index.js", "module": "./dist/esm/index.js", "exports": {"import": {"types": "./dist/types/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/types/index.d.ts", "default": "./dist/cjs/index.js"}}, "scripts": {"build:test": "npm --prefix ./dist-test test", "build:types": "tsc -p tsconfig.types.json", "build:cjs": "tsc -p tsconfig.cjs.json && tsx postcjs.ts", "build:esm": "tsc -p tsconfig.esm.json && tsx postesm.ts", "build": "npm i && npm run gen && npm test && rimraf ./dist && npm run build:types && npm run build:cjs && npm run build:esm && npm run build:test", "dry": "npm run build && npm pub --dry-run", "test:watch": "tsx watch test/index.ts", "test:gen": "tsx test/createIndex.ts", "test": "tsx test/index.ts", "gen": "tsx createIndex.ts"}, "c8": {"exclude": ["createIndex.ts", "postcjs.ts", "postesm.ts", "test"]}, "keywords": ["zod", "json", "schema", "open", "api", "conversion"], "author": "<PERSON>", "contributors": ["<PERSON><PERSON> (https://github.com/mrhammadasif)", "<PERSON> (https://github.com/Noah2610)", "<PERSON> (https://github.com/johngeorgewright)", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (https://github.com/krzysztofciombor)", "<PERSON><PERSON> (https://github.com/mokocm)", "<PERSON> (https://github.com/tomarad)", "<PERSON> (https://github.com/iway1)", "<PERSON> (https://github.com/Andy2003)", "<PERSON> (https://github.com/Janpot)", "<PERSON> (https://github.com/scammi)", "<PERSON> (https://github.com/Planeshifter)", "<PERSON> (https://github.com/Bram-dc)", "<PERSON><PERSON> (https://github.com/gthecht)", "<PERSON> (https://github.com/colinhacks)", "Spappz (https://github.com/Spappz)", "<PERSON> (https://github.com/jacoblee93)", "<PERSON> (https://github.com/brettz9)", "<PERSON> (https://github.com/imsanchez)", "<PERSON> (https://github.com/mitchell-merry)", "<PERSON><PERSON> (https://github.com/enzomonjardin)", "<PERSON><PERSON><PERSON> (https://github.com/NanezX)"], "repository": {"type": "git", "url": "https://github.com/StefanTerdell/zod-to-json-schema"}, "license": "ISC", "peerDependencies": {"zod": "^3.24.1"}, "devDependencies": {"@types/json-schema": "^7.0.9", "@types/node": "^20.9.0", "ajv": "^8.6.3", "ajv-errors": "^3.0.0", "ajv-formats": "^2.1.1", "fast-diff": "^1.3.0", "local-ref-resolver": "^0.2.0", "rimraf": "^3.0.2", "tsx": "^4.19.0", "typescript": "^5.1.3", "zod": "^3.24.1"}}